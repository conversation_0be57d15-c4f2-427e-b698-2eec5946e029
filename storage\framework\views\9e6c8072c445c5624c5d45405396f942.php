<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
        <?php if(auth()->guard()->check()): ?>
            <meta name="user-name" content="<?php echo e(auth()->user()->name); ?>">
            <meta name="user-avatar" content="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>">
        <?php endif; ?>
        <?php if(auth()->guard()->check()): ?>
        <meta name="user-id" content="<?php echo e(auth()->id()); ?>">
        <meta name="user-role" content="<?php echo e(auth()->user()->role); ?>">
        <?php endif; ?>

        <title><?php echo e(config('app.name', 'UniLink')); ?></title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
        <script src="<?php echo e(asset('js/post-summary-updater.js')); ?>"></script>
        <script src="<?php echo e(asset('js/comments.js')); ?>"></script>
        <script src="<?php echo e(asset('js/comment-modal.js')); ?>"></script>
        <script src="<?php echo e(asset('js/reactions.js')); ?>"></script>
        <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

    </head>
    <body class="font-sans antialiased bg-gray-100 overflow-hidden">
        <!-- Header Navigation -->
        <?php echo $__env->make('layouts.unilink-header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <!-- Main 3-Column Container -->
        <div class="flex h-screen pt-16">
            <!-- Left Sidebar -->
            <div class="hidden lg:flex lg:flex-col lg:w-64 xl:w-72 bg-white border-r border-gray-200 overflow-y-auto flex-shrink-0">
                <?php echo $__env->make('layouts.unilink-sidebar-content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>

            <!-- Central Feed -->
            <main class="flex-1 bg-gray-100 overflow-y-auto min-w-0">
                <div class="max-w-6xl mx-auto px-4 py-6">
                    <?php echo e($slot); ?>

                </div>
            </main>

            <!-- Right Sidebar -->
            <div class="hidden lg:flex lg:flex-col lg:w-80 xl:w-96 bg-white border-l border-gray-200 overflow-y-auto flex-shrink-0 force-show-right-sidebar">
                <?php echo $__env->make('layouts.unilink-right-sidebar-content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>

        <!-- Mobile Sidebar -->
        <div class="lg:hidden">
            <?php echo $__env->make('layouts.unilink-sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div x-data="{ open: false }"
             x-on:toggle-sidebar.window="open = !open"
             x-show="open"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-75 z-30 lg:hidden"
             @click="$dispatch('toggle-sidebar')">
        </div>

        <!-- Notification Popup -->
        <?php echo $__env->make('components.notification-popup', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <!-- Image Modal -->
        <?php echo $__env->make('components.image-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>

    </body>
</html>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/unilink-layout.blade.php ENDPATH**/ ?>