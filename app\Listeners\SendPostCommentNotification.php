<?php

namespace App\Listeners;

use App\Events\PostCommentAdded;
use App\Notifications\PostCommented;
use App\Notifications\CommentReplied;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendPostCommentNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PostCommentAdded $event): void
    {
        // If this is a reply to another comment
        if ($event->comment->parent_id) {
            $parentComment = $event->comment->parent;
            
            // Don't notify if user replied to their own comment
            if ($event->user->id !== $parentComment->user_id) {
                // Check if the parent comment owner wants to receive this type of notification
                if ($parentComment->user->wantsNotification('comment_replies')) {
                    $parentComment->user->notify(new CommentReplied($event->user, $parentComment, $event->comment));
                }
            }
        } else {
            // This is a direct comment on the post
            // Don't notify if user commented on their own post
            if ($event->user->id !== $event->post->user_id) {
                // Check if the post owner wants to receive this type of notification
                if ($event->post->user->wantsNotification('post_comments')) {
                    $event->post->user->notify(new PostCommented($event->user, $event->post, $event->comment));
                }
            }
        }
    }
}
