<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'student_id',
        'phone',
        'bio',
        'avatar',
        'background_photo',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Check if user is a student
     */
    public function isStudent(): bool
    {
        return $this->role === 'student';
    }

    /**
     * Check if user is an organization officer
     */
    public function isOrgOfficer(): bool
    {
        return $this->role === 'org_officer';
    }

    /**
     * Check if user is an admin
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user has admin or org officer privileges
     */
    public function hasManagementAccess(): bool
    {
        return in_array($this->role, ['admin', 'org_officer']);
    }

    /**
     * Get organizations this user belongs to
     */
    public function organizations(): BelongsToMany
    {
        return $this->belongsToMany(Organization::class, 'organization_members')
            ->using(OrganizationMember::class)
            ->withPivot(['role', 'status', 'joined_at'])
            ->withTimestamps();
    }

    /**
     * Get active organizations this user belongs to
     */
    public function activeOrganizations(): BelongsToMany
    {
        return $this->belongsToMany(Organization::class, 'organization_members')
            ->using(OrganizationMember::class)
            ->withPivot(['role', 'status', 'joined_at'])
            ->withTimestamps()
            ->wherePivot('status', 'active')
            ->where('organizations.status', 'active');
    }

    /**
     * Get organizations this user created
     */
    public function createdOrganizations(): HasMany
    {
        return $this->hasMany(Organization::class, 'created_by');
    }

    /**
     * Get posts created by this user
     */
    public function posts(): HasMany
    {
        return $this->hasMany(Post::class);
    }

    /**
     * Get scholarships created by this user
     */
    public function scholarships(): HasMany
    {
        return $this->hasMany(Scholarship::class, 'created_by');
    }

    /**
     * Get organizations this user is following
     */
    public function followedOrganizations(): BelongsToMany
    {
        return $this->belongsToMany(Organization::class, 'organization_followers')
            ->withTimestamps();
    }

    /**
     * Get groups this user belongs to
     */
    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class, 'group_members')
            ->using(GroupMember::class)
            ->withPivot(['role', 'status', 'joined_at'])
            ->withTimestamps();
    }

    /**
     * Get active groups this user belongs to
     */
    public function activeGroups(): BelongsToMany
    {
        return $this->groups()->wherePivot('status', 'active');
    }

    /**
     * Get pending group memberships for this user
     */
    public function pendingGroups(): BelongsToMany
    {
        return $this->groups()->wherePivot('status', 'pending');
    }

    /**
     * Get groups this user created
     */
    public function createdGroups(): HasMany
    {
        return $this->hasMany(Group::class, 'created_by');
    }

    /**
     * Check if user is following an organization
     */
    public function isFollowing(Organization $organization): bool
    {
        return $this->followedOrganizations()->where('organization_id', $organization->id)->exists();
    }

    /**
     * Get comments made by this user
     */
    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class);
    }

    /**
     * Get likes made by this user
     */
    public function likes(): HasMany
    {
        return $this->hasMany(Like::class);
    }

    /**
     * Get shares made by this user
     */
    public function shares(): HasMany
    {
        return $this->hasMany(Share::class);
    }

    /**
     * Get users who are following this user
     */
    public function followers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_followers', 'followed_id', 'follower_id')
            ->withTimestamps();
    }

    /**
     * Get users this user is following
     */
    public function following(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_followers', 'follower_id', 'followed_id')
            ->withTimestamps();
    }

    /**
     * Check if this user is following another user
     */
    public function isFollowingUser(User $user): bool
    {
        return $this->following()->where('followed_id', $user->id)->exists();
    }

    /**
     * Check if this user is followed by another user
     */
    public function isFollowedByUser(User $user): bool
    {
        return $this->followers()->where('follower_id', $user->id)->exists();
    }

    /**
     * Follow another user
     */
    public function followUser(User $user): bool
    {
        if ($this->id === $user->id) {
            return false; // Can't follow yourself
        }

        if ($this->isFollowingUser($user)) {
            return false; // Already following
        }

        $this->following()->attach($user->id);

        // Send notification to the followed user
        $user->notify(new \App\Notifications\UserFollowed($this));

        return true;
    }

    /**
     * Unfollow another user
     */
    public function unfollowUser(User $user): bool
    {
        if (!$this->isFollowingUser($user)) {
            return false; // Not following
        }

        $this->following()->detach($user->id);
        return true;
    }
}
