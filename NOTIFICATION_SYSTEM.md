# UniLink Real-Time Notification System

## Recent Updates

### Real Profile Pictures in Notifications (Latest)
- **Enhanced User Experience**: Notifications now display actual user profile pictures instead of generated avatars
- **Smart Fallback System**: Real photos → Generated avatars → Default fallback
- **Optimized Performance**: New `getNotificationAvatarUrl()` method for consistent sizing
- **Better Visual Recognition**: Users can easily identify who interacted with their content

## Overview

The UniLink platform now includes a comprehensive real-time notification system that captures and displays all relevant events such as post interactions, user follows, organization activities, and more.

## Features

- **Real-time notifications** using Laravel Echo and Pusher
- **Multiple notification types**: post reactions, comments, shares, follows, organization posts, group activities
- **User preferences** to control which notifications to receive
- **Read/unread states** with visual indicators
- **Browser notifications** (with user permission)
- **Responsive UI** with dropdown notification panel in header

## Setup Instructions

### 1. Environment Configuration

Add the following to your `.env` file:

```env
BROADCAST_CONNECTION=pusher
QUEUE_CONNECTION=database

# Pusher Configuration
PUSHER_APP_ID=your-app-id
PUSHER_APP_KEY=your-app-key
PUSHER_APP_SECRET=your-app-secret
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

# Vite Environment Variables
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
```

### 2. Database Migration

Run the migration to add notification preferences to users:

```bash
php artisan migrate
```

### 3. Queue Worker

Start the queue worker to process notifications:

```bash
php artisan queue:work
```

### 4. Frontend Assets

Build the frontend assets with the new Echo configuration:

```bash
npm run build
# or for development
npm run dev
```

## Notification Types

### Post Interactions
- **Post Reactions**: When someone reacts to your post
- **Post Comments**: When someone comments on your post
- **Post Shares**: When someone shares your post
- **Comment Replies**: When someone replies to your comment

### Social
- **User Follows**: When someone follows you

### Organizations & Groups
- **Organization Posts**: New posts from organizations you follow
- **Group Posts**: New posts in groups you're a member of
- **Group Memberships**: Group membership requests and approvals

### System
- **Scholarship Updates**: New scholarships and updates
- **Admin Notifications**: Important system announcements

## API Endpoints

### Get Notifications
```
GET /notifications
```
Returns user's notifications with pagination and read/unread status.

### Mark as Read
```
POST /notifications/{id}/read
```
Marks a specific notification as read.

### Mark All as Read
```
POST /notifications/read-all
```
Marks all notifications as read for the current user.

### Get Unread Count
```
GET /notifications/unread-count
```
Returns the count of unread notifications.

### Delete Notification
```
DELETE /notifications/{id}
```
Deletes a specific notification.

## User Preferences

Users can control their notification preferences by visiting `/notification-preferences`. Available options include:

- Enable/disable all notifications
- Control specific notification types
- Granular control over post interactions, social activities, and system notifications

## Real-time Updates

The system uses Laravel Echo with Pusher to provide real-time updates. Notifications appear instantly in the header dropdown without requiring page refresh.

### Browser Notifications

The system supports browser notifications for important updates even when the user is not actively viewing the page. Key features:

- **User-initiated permission requests**: Notification permission is only requested when the user interacts with the notification dropdown
- **Smart prompting**: A friendly prompt appears in the notification dropdown for users who haven't granted permission
- **Graceful fallback**: The system works perfectly without browser notifications if permission is denied
- **No console errors**: Permission requests are properly handled to avoid browser console warnings

## Testing

Run the notification system tests:

```bash
php artisan test --filter=NotificationSystemTest
```

## Customization

### Adding New Notification Types

1. Create a new notification class:
```bash
php artisan make:notification YourNotification
```

2. Create an event class:
```bash
php artisan make:event YourEvent
```

3. Create a listener:
```bash
php artisan make:listener YourListener
```

4. Register the event-listener mapping in `EventServiceProvider`

5. Add the notification type to user preferences in the `User` model

### Customizing Notification Templates

Notification templates are defined in the notification classes. Each notification includes:
- Database representation (`toArray` method)
- Broadcast representation (`toBroadcast` method)
- Message formatting and URL generation

## Testing

Run the notification system tests:

```bash
php artisan test --filter=NotificationSystemTest
```

### Manual Testing

You can also test the notification system manually using the browser console:

```javascript
// Load the test script (add to your layout if needed)
// <script src="{{ asset('js/notification-test.js') }}"></script>

// Test browser notifications
testNotificationSystem();

// Test API endpoints
testNotificationAPI();

// Test real-time connection
testEchoConnection();
```

## Troubleshooting

### Notifications Not Appearing
1. Check that the queue worker is running
2. Verify Pusher credentials are correct
3. Ensure broadcasting is enabled in `.env`
4. Check browser console for JavaScript errors

### Real-time Updates Not Working
1. Verify Laravel Echo is properly configured
2. Check Pusher connection in browser developer tools
3. Ensure channels are properly authenticated

### Browser Notification Permission Issues
1. The system now properly handles permission requests only on user interaction
2. If you see console errors about notification permissions, ensure you're using the updated header code
3. Users can manually enable notifications through the dropdown prompt

### Profile Picture Display
1. **Real Profile Pictures First**: System now prioritizes actual uploaded profile pictures over generated avatars
2. **New User Methods**:
   - `getNotificationAvatarUrl()`: Optimized for notification display with proper sizing
   - `hasRealProfilePicture()`: Check if user has uploaded a real profile picture
   - Enhanced `getAvatarUrl()`: Better fallback handling
3. **Fallback Strategy**:
   - Primary: Real uploaded profile picture from `avatar` field
   - Secondary: Generated avatar from `ui-avatars.com` service
   - Tertiary: Local default avatar (for error cases)
4. **Consistent Display**: All notification types now show real profile pictures when available

### Blade Template Issues
1. Fixed conflict between Blade `@error` directive and Alpine.js `@error` event handler
2. Use `@@error` (double @) to escape Blade directives when needed in Alpine.js
3. Ensure all Blade directives are properly closed to avoid compilation errors

### Performance Issues
1. Consider using Redis for queue driver in production
2. Implement notification cleanup for old notifications
3. Use database indexing for notification queries

## Security Considerations

- Notifications are only sent to authorized users
- Private channels ensure users only receive their own notifications
- User preferences are respected to prevent spam
- All notification endpoints require authentication
