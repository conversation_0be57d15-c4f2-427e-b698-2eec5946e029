<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'UniLink') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="font-sans text-custom-darkest antialiased">
        <!-- Background with gradient -->
        <div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gradient-to-br from-custom-lightest via-gray-50 to-custom-lightest">
            <!-- Logo Section -->
            <div class="mb-8">
                <a href="/" class="flex flex-col items-center group">
                    <x-application-logo class="w-16 h-16 fill-current text-custom-green transition-transform duration-300 group-hover:scale-110" />
                    <h1 class="mt-3 text-2xl font-bold text-custom-darkest">UniLink</h1>
                    <p class="text-sm text-custom-dark-gray mt-1">Connect. Learn. Grow.</p>
                </a>
            </div>

            <!-- Form Container -->
            <div class="w-full sm:max-w-md">
                <div class="bg-white shadow-xl border border-custom-lightest overflow-hidden sm:rounded-2xl">
                    <div class="px-8 py-8">
                        {{ $slot }}
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="mt-8 text-center">
                <p class="text-sm text-custom-dark-gray">
                    © {{ date('Y') }} UniLink. Connecting university communities.
                </p>
            </div>
        </div>
    </body>
</html>
