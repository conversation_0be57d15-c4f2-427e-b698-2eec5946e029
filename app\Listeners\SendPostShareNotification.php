<?php

namespace App\Listeners;

use App\Events\PostSharedEvent;
use App\Notifications\PostShared;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendPostShareNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PostSharedEvent $event): void
    {
        // Don't notify if user shared their own post
        if ($event->user->id === $event->post->user_id) {
            return;
        }

        // Check if the post owner wants to receive this type of notification
        if (!$event->post->user->wantsNotification('post_shares')) {
            return;
        }

        // Send notification to the post owner
        $event->post->user->notify(new PostShared($event->user, $event->post, $event->share));
    }
}
