<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Post;
use App\Models\Share;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class PostShared extends Notification implements ShouldQueue
{
    use Queueable;

    public $user;
    public $post;
    public $share;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, Post $post, Share $share)
    {
        $this->user = $user;
        $this->post = $post;
        $this->share = $share;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'post_shared',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getNotificationAvatarUrl(),
            'post_id' => $this->post->id,
            'post_title' => $this->post->title,
            'share_id' => $this->share->id,
            'share_message' => $this->share->message,
            'message' => $this->getMessage(),
            'url' => $this->getShareUrl(),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'post_shared',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getNotificationAvatarUrl(),
            'post_id' => $this->post->id,
            'post_title' => $this->post->title,
            'share_id' => $this->share->id,
            'share_message' => $this->share->message,
            'message' => $this->getMessage(),
            'url' => $this->getShareUrl(),
        ];
    }

    /**
     * Get the notification message
     */
    private function getMessage(): string
    {
        return "{$this->user->name} shared your post";
    }

    /**
     * Get the share URL
     */
    private function getShareUrl(): string
    {
        return route('profile.user', $this->user) . '#share-' . $this->share->id;
    }
}
