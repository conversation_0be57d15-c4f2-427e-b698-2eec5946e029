<?php

namespace App\Listeners;

use App\Events\PostReactionAdded;
use App\Notifications\PostReacted;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendPostReactionNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PostReactionAdded $event): void
    {
        // Don't notify if user reacted to their own post
        if ($event->user->id === $event->post->user_id) {
            return;
        }

        // Check if the post owner wants to receive this type of notification
        if (!$event->post->user->wantsNotification('post_reactions')) {
            return;
        }

        // Send notification to the post owner
        $event->post->user->notify(new PostReacted($event->user, $event->post, $event->reaction));
    }
}
