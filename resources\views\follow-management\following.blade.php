<x-unilink-layout>
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Following</h1>
                    <p class="text-gray-600 mt-1">Manage your connections</p>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ route('follow-management.followers') }}" 
                       class="text-blue-600 hover:text-blue-800 font-medium transition-colors">
                        View Followers
                    </a>
                    <a href="{{ route('profile.show') }}" 
                       class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                        Back to Profile
                    </a>
                </div>
            </div>
        </div>

        <!-- Tabs Navigation -->
        <div class="bg-white rounded-lg shadow-sm mb-6">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6" aria-label="Tabs">
                    <a href="{{ route('follow-management.following', ['tab' => 'users']) }}" 
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors {{ $currentTab === 'users' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            <span>Users</span>
                            @if(isset($following))
                                <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">{{ $following->total() }}</span>
                            @endif
                        </div>
                    </a>
                    <a href="{{ route('follow-management.following', ['tab' => 'organizations']) }}" 
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors {{ $currentTab === 'organizations' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                            <span>Organizations</span>
                            @if(isset($organizations))
                                <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">{{ $organizations->total() }}</span>
                            @endif
                        </div>
                    </a>
                    <a href="{{ route('follow-management.following', ['tab' => 'groups']) }}" 
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors {{ $currentTab === 'groups' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <span>Groups</span>
                            @if(isset($groups))
                                <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">{{ $groups->total() }}</span>
                            @endif
                        </div>
                    </a>
                    <a href="{{ route('follow-management.following', ['tab' => 'discover']) }}"
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors {{ $currentTab === 'discover' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            <span>Discover Users</span>
                            @if(isset($allUsers))
                                <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs">{{ $allUsers->total() }}</span>
                            @endif
                        </div>
                    </a>
                </nav>
            </div>

            <!-- Search Bar -->
            <div class="p-6">
                <form method="GET" action="{{ route('follow-management.following') }}" class="flex items-center space-x-4">
                    <input type="hidden" name="tab" value="{{ $currentTab }}">
                    <div class="flex-1">
                        <div class="relative">
                            <input type="text"
                                   name="search"
                                   value="{{ $search }}"
                                   placeholder="@if($currentTab === 'discover')Search users by name, email, or bio...@else Search {{ $currentTab }}...@endif"
                                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </div>
                        </div>
                    </div>
                    <button type="submit" 
                            class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        Search
                    </button>
                    @if($search)
                        <a href="{{ route('follow-management.following', ['tab' => $currentTab]) }}" 
                           class="text-gray-500 hover:text-gray-700 px-3 py-2 transition-colors">
                            Clear
                        </a>
                    @endif
                </form>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="bg-white rounded-lg shadow-sm">
            @if($currentTab === 'users' && isset($following))
                @include('follow-management.partials.users-tab', ['items' => $following])
            @elseif($currentTab === 'organizations' && isset($organizations))
                @include('follow-management.partials.organizations-tab', ['items' => $organizations])
            @elseif($currentTab === 'groups' && isset($groups))
                @include('follow-management.partials.groups-tab', ['items' => $groups])
            @elseif($currentTab === 'discover' && isset($allUsers))
                @include('follow-management.partials.discover-tab', ['items' => $allUsers])
            @endif
        </div>
    </div>
</x-unilink-layout>
