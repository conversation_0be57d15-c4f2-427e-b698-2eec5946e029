<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Group;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class GroupMembershipRequest extends Notification implements ShouldQueue
{
    use Queueable;

    public $user;
    public $group;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, Group $group)
    {
        $this->user = $user;
        $this->group = $group;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'group_membership_request',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($this->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($this->user->name) . '&color=7BC74D&background=EEEEEE',
            'group_id' => $this->group->id,
            'group_name' => $this->group->name,
            'group_logo' => $this->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($this->group->logo) : null,
            'message' => $this->getMessage(),
            'url' => $this->getGroupUrl(),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'group_membership_request',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($this->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($this->user->name) . '&color=7BC74D&background=EEEEEE',
            'group_id' => $this->group->id,
            'group_name' => $this->group->name,
            'group_logo' => $this->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($this->group->logo) : null,
            'message' => $this->getMessage(),
            'url' => $this->getGroupUrl(),
        ];
    }

    /**
     * Get the notification message
     */
    private function getMessage(): string
    {
        return "{$this->user->name} requested to join {$this->group->name}";
    }

    /**
     * Get the group URL
     */
    private function getGroupUrl(): string
    {
        return route('groups.show', $this->group->slug) . '?tab=members';
    }
}
