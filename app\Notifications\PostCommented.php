<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Post;
use App\Models\Comment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class PostCommented extends Notification implements ShouldQueue
{
    use Queueable;

    public $user;
    public $post;
    public $comment;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, Post $post, Comment $comment)
    {
        $this->user = $user;
        $this->post = $post;
        $this->comment = $comment;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'post_commented',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getAvatarUrl(64),
            'post_id' => $this->post->id,
            'post_title' => $this->post->title,
            'comment_id' => $this->comment->id,
            'comment_content' => $this->getCommentPreview(),
            'message' => $this->getMessage(),
            'url' => $this->getPostUrl(),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'post_commented',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getAvatarUrl(64),
            'post_id' => $this->post->id,
            'post_title' => $this->post->title,
            'comment_id' => $this->comment->id,
            'comment_content' => $this->getCommentPreview(),
            'message' => $this->getMessage(),
            'url' => $this->getPostUrl(),
        ];
    }

    /**
     * Get the notification message
     */
    private function getMessage(): string
    {
        return "{$this->user->name} commented on your post";
    }

    /**
     * Get comment preview (first 100 characters)
     */
    private function getCommentPreview(): string
    {
        return \Illuminate\Support\Str::limit($this->comment->content, 100);
    }

    /**
     * Get the post URL
     */
    private function getPostUrl(): string
    {
        if ($this->post->group_id) {
            return route('groups.show', $this->post->group->slug) . '#comment-' . $this->comment->id;
        } elseif ($this->post->organization_id) {
            return route('organizations.show', $this->post->organization->slug) . '#comment-' . $this->comment->id;
        } else {
            return route('profile.user', $this->post->user) . '#comment-' . $this->comment->id;
        }
    }
}
