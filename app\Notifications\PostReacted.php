<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Post;
use App\Models\Reaction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class PostReacted extends Notification implements ShouldQueue
{
    use Queueable;

    public $user;
    public $post;
    public $reaction;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, Post $post, Reaction $reaction)
    {
        $this->user = $user;
        $this->post = $post;
        $this->reaction = $reaction;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'post_reacted',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getAvatarUrl(64),
            'post_id' => $this->post->id,
            'post_title' => $this->post->title,
            'reaction_type' => $this->reaction->type,
            'reaction_emoji' => $this->getReactionEmoji($this->reaction->type),
            'message' => $this->getMessage(),
            'url' => $this->getPostUrl(),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'post_reacted',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getAvatarUrl(64),
            'post_id' => $this->post->id,
            'post_title' => $this->post->title,
            'reaction_type' => $this->reaction->type,
            'reaction_emoji' => $this->getReactionEmoji($this->reaction->type),
            'message' => $this->getMessage(),
            'url' => $this->getPostUrl(),
        ];
    }

    /**
     * Get the notification message
     */
    private function getMessage(): string
    {
        $reactionLabel = $this->getReactionLabel($this->reaction->type);
        return "{$this->user->name} reacted {$reactionLabel} to your post";
    }

    /**
     * Get the post URL
     */
    private function getPostUrl(): string
    {
        if ($this->post->group_id) {
            return route('groups.show', $this->post->group->slug) . '#post-' . $this->post->id;
        } elseif ($this->post->organization_id) {
            return route('organizations.show', $this->post->organization->slug) . '#post-' . $this->post->id;
        } else {
            return route('profile.user', $this->post->user) . '#post-' . $this->post->id;
        }
    }

    /**
     * Get reaction emoji
     */
    private function getReactionEmoji(string $type): string
    {
        $emojis = [
            'like' => '👍',
            'love' => '❤️',
            'haha' => '😂',
            'wow' => '😮',
            'sad' => '😢',
            'angry' => '😠',
        ];

        return $emojis[$type] ?? '👍';
    }

    /**
     * Get reaction label
     */
    private function getReactionLabel(string $type): string
    {
        $labels = [
            'like' => 'with a like',
            'love' => 'with love',
            'haha' => 'with laughter',
            'wow' => 'with wow',
            'sad' => 'with sadness',
            'angry' => 'with anger',
        ];

        return $labels[$type] ?? 'with a like';
    }
}
